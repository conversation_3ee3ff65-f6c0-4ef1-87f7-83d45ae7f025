"""Constants for the orchestrator module.

This module centralizes all magic numbers and configuration constants
used across the orchestrator components for better maintainability.
"""

# ============================================================================
# Phase 1 Strategy Generation Constants
# ============================================================================

# File processing limits
MAX_FILE_SIZE_MB = 10
DEFAULT_TIMEOUT_SECONDS = 60
MAX_SIMILAR_EXPERIENCES = 5

# Complexity scoring thresholds
COMPLEXITY_SIMPLE_THRESHOLD = 2
COMPLEXITY_MODERATE_THRESHOLD = 5

# Complexity score weights
COMPLEXITY_SCORE_SIMPLE = 0
COMPLEXITY_SCORE_BASIC = 1
COMPLEXITY_SCORE_MODERATE = 2
COMPLEXITY_SCORE_COMPLEX = 3

# Feature scoring weights
FEATURE_SCORE_LOOPS = 2
FEATURE_SCORE_FUNCTIONS = 1

# LLM retry limits
DEFAULT_LLM_MAX_RETRIES = 3
CODE_GENERATION_MAX_RETRIES = 2
FALLBACK_MAX_RETRIES = 1

# ============================================================================
# Phase 3 Production Constants
# ============================================================================

# Telemetry collection settings
TELEMETRY_BATCH_SIZE = 1000
TELEMETRY_COLLECTION_INTERVAL = 0.1  # seconds

# Analysis intervals
LLM_ANALYSIS_INTERVAL_MINUTES = 10
ANOMALY_CHECK_INTERVAL_SECONDS = 60

# Task management
MAX_TASK_RESTART_ATTEMPTS = 3
TASK_HEALTH_CHECK_INTERVAL = 60  # seconds

# Timeout settings
COMPONENT_SHUTDOWN_TIMEOUT = 5.0  # seconds
TELEMETRY_TIMEOUT = 5.0  # seconds

# LLM analysis settings
ANALYSIS_MAX_TOKENS = 500
ANALYSIS_TEMPERATURE = 0.3
ANALYSIS_RETRY_MAX = 2

# Strategy generation settings
STRATEGY_MAX_TOKENS = 1000
STRATEGY_TEMPERATURE = 0.7

# Exponential backoff base
BACKOFF_BASE = 2

# ============================================================================
# Shadow Process Manager Constants
# ============================================================================

# Shadow process limits
DEFAULT_MAX_SHADOWS = 3
MIN_PERFORMANCE_HISTORY = 3
MAX_RETRY_ATTEMPTS = 3

# Time windows and intervals
DEFAULT_PERFORMANCE_WINDOW = 300  # 5 minutes in seconds
HEALTH_CHECK_INTERVAL = 30  # seconds
RESOURCE_CHECK_INTERVAL = 10  # seconds

# Performance thresholds
DEFAULT_PROMOTION_THRESHOLD = 0.1  # 10% improvement
DEFAULT_RESOURCE_LIMIT = 0.2  # 20% CPU

# Resource limits
DEFAULT_SHADOW_TIMEOUT_HOURS = 1.0
MAX_MEMORY_USAGE_MB = 512  # Per shadow process

# Performance history and queues
PERFORMANCE_HISTORY_MAXLEN = 10
CHAMPION_METRICS_MAXLEN = 10

# Health monitoring
MAX_HEALTH_CHECK_FAILURES = 3
MIN_PERFORMANCE_SAMPLES = 5

# Event processing
EVENT_QUEUE_TIMEOUT = 1.0  # seconds

# Performance calculation weights
TIME_WEIGHT_DECAY = 0.1  # Per sample age

# ============================================================================
# Campaign Orchestrator Constants
# ============================================================================

# Default campaign duration
DEFAULT_CAMPAIGN_HOURS = 2.0

# Phase completion markers
PHASE_0_COMPLETED = 1
PHASE_1_COMPLETED = 2
PHASE_2_COMPLETED = 3
PHASE_3_COMPLETED = 4
PHASE_4_COMPLETED = 5
PHASE_5_COMPLETED = 6

# Initial strategy changes counter
INITIAL_STRATEGY_CHANGES = 0

# Cleanup timeout
CLEANUP_TIMEOUT = 5.0  # seconds

# Reflection confidence scores
HIGH_COVERAGE_THRESHOLD = 0.8
LOW_COVERAGE_THRESHOLD = 0.3
HIGH_CONFIDENCE_SCORE = 0.9
LOW_CONFIDENCE_SCORE = 0.5

# JSON formatting
JSON_INDENT = 2

# ============================================================================
# Validation Constants
# ============================================================================

# Security pattern detection threshold
DANGEROUS_PATTERN_THRESHOLD = 3

# ============================================================================
# Workspace and File Processing
# ============================================================================

# Default workspace settings
DEFAULT_WORKSPACE_DIR = "./workspace"
DEFAULT_SEED_COUNT = 8

# Supported file extensions
SUPPORTED_C_EXTENSIONS = {".c", ".cpp", ".cc", ".cxx", ".c++"}
